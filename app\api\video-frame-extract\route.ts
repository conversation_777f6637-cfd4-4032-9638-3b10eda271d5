import { NextRequest, NextResponse } from 'next/server'
import path from 'path'
import fs from 'fs/promises'
import { existsSync } from 'fs'

// 临时文件存储目录
const TEMP_DIR = path.join(process.cwd(), 'data', 'temp', 'video-frames')

// 确保临时目录存在
async function ensureTempDir() {
  try {
    if (!existsSync(TEMP_DIR)) {
      await fs.mkdir(TEMP_DIR, { recursive: true })
    }
  } catch (error) {
    console.error('创建临时目录失败:', error)
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const videoFile = formData.get('video') as File
    const config = JSON.parse(formData.get('config') as string)
    
    if (!videoFile) {
      return NextResponse.json(
        { error: '视频文件未提供' },
        { status: 400 }
      )
    }

    // 验证配置参数
    const {
      frameInterval = 30,
      skipStartTime = 0,
      skipEndTime = 0,
      imageQuality = 'medium'
    } = config

    // 确保临时目录存在
    await ensureTempDir()

    // 生成唯一的任务ID
    const taskId = `video-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const taskDir = path.join(TEMP_DIR, taskId)
    await fs.mkdir(taskDir, { recursive: true })

    // 保存上传的视频文件
    const videoBuffer = Buffer.from(await videoFile.arrayBuffer())
    const videoPath = path.join(taskDir, 'video.mp4')
    await fs.writeFile(videoPath, videoBuffer)

    // 返回任务ID，客户端可以用来查询进度
    return NextResponse.json({
      success: true,
      taskId,
      message: '视频文件已上传，开始处理...'
    })

  } catch (error: any) {
    console.error('视频帧提取API错误:', error)
    
    return NextResponse.json(
      { 
        error: '服务器内部错误',
        details: error.message
      },
      { status: 500 }
    )
  }
}

// 清理临时文件
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const taskId = searchParams.get('taskId')
    
    if (!taskId) {
      return NextResponse.json(
        { error: '任务ID未提供' },
        { status: 400 }
      )
    }

    const taskDir = path.join(TEMP_DIR, taskId)
    
    if (existsSync(taskDir)) {
      await fs.rm(taskDir, { recursive: true, force: true })
    }

    return NextResponse.json({
      success: true,
      message: '临时文件已清理'
    })

  } catch (error: any) {
    console.error('清理临时文件失败:', error)
    
    return NextResponse.json(
      { 
        error: '清理失败',
        details: error.message
      },
      { status: 500 }
    )
  }
}

// 获取处理进度
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const taskId = searchParams.get('taskId')
    
    if (!taskId) {
      return NextResponse.json(
        { error: '任务ID未提供' },
        { status: 400 }
      )
    }

    const taskDir = path.join(TEMP_DIR, taskId)
    const statusFile = path.join(taskDir, 'status.json')
    
    if (!existsSync(statusFile)) {
      return NextResponse.json({
        success: false,
        error: '任务不存在或已完成'
      })
    }

    const statusData = await fs.readFile(statusFile, 'utf-8')
    const status = JSON.parse(statusData)

    return NextResponse.json({
      success: true,
      status
    })

  } catch (error: any) {
    console.error('获取处理进度失败:', error)
    
    return NextResponse.json(
      { 
        error: '获取进度失败',
        details: error.message
      },
      { status: 500 }
    )
  }
}
