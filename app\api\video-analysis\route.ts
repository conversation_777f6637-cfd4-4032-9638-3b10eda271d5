import { NextRequest, NextResponse } from 'next/server'

// 硅基流动API配置
const SILICONFLOW_API_BASE = 'https://api.siliconflow.cn/v1'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      image, 
      timestamp, 
      apiKey, 
      model = 'Qwen/Qwen2.5-VL-32B-Instruct' 
    } = body

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API密钥未提供' },
        { status: 400 }
      )
    }

    if (!image) {
      return NextResponse.json(
        { error: '图像数据未提供' },
        { status: 400 }
      )
    }

    // 构建视频分析的专用prompt
    const analysisPrompt = `请仔细分析这张视频帧图片（时间戳: ${timestamp}秒），提供详细的内容分析：

🎯 分析要求：
1. **字幕文本提取**：
   - 识别并提取画面中的所有字幕文本
   - 区分对话字幕、解说字幕、标题文字
   - 如果没有字幕，明确标注"无字幕"

2. **场景描述**：
   - 描述画面的主要场景和环境
   - 包括室内/室外、时间（白天/夜晚）、地点特征
   - 注意画面的整体氛围和色调

3. **人物行为分析**：
   - 识别画面中的人物（真人或动画角色）
   - 描述人物的主要动作和表情
   - 分析人物之间的互动关系

4. **关键情节要素**：
   - 识别重要的道具、物品
   - 注意特殊效果或动作场面
   - 捕捉情节转折的视觉线索

📊 请严格按照以下JSON格式回答：
{
  "hasSubtitles": boolean,
  "subtitleText": "提取的字幕文本，如果没有则为空字符串",
  "sceneDescription": "场景描述，50-100字",
  "characterActions": ["人物行为1", "人物行为2"],
  "keyElements": ["关键要素1", "关键要素2"],
  "confidence": number,
  "timestamp": ${timestamp}
}

⚠️ 注意：
- 字幕检测要准确，只有确认是对话或解说字幕才提取
- 场景描述要具体生动，有助于理解剧情
- 人物行为要具体，避免泛泛而谈
- 关键要素要突出本帧的独特信息`

    // 调用硅基流动API
    const response = await fetch(`${SILICONFLOW_API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model,
        messages: [{
          role: "user",
          content: [
            {
              type: "image_url",
              image_url: {
                url: image
              }
            },
            {
              type: "text",
              text: analysisPrompt
            }
          ]
        }],
        temperature: 0.1,
        max_tokens: 800,
        stream: false
      })
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('硅基流动API错误:', response.status, errorData)
      
      return NextResponse.json(
        { 
          error: `API调用失败: ${response.status}`,
          details: errorData
        },
        { status: response.status }
      )
    }

    const data = await response.json()
    
    // 验证响应格式
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      return NextResponse.json(
        { error: 'API返回格式异常' },
        { status: 500 }
      )
    }

    const content = data.choices[0].message.content
    
    // 尝试解析JSON响应
    let analysis
    try {
      analysis = JSON.parse(content)
    } catch (parseError) {
      // 如果不是JSON格式，尝试从文本中提取信息
      console.warn('JSON解析失败，使用文本分析:', content)
      analysis = parseTextAnalysis(content, timestamp)
    }

    // 验证和补全分析结果
    analysis = validateAnalysisResult(analysis, timestamp)

    return NextResponse.json({
      success: true,
      analysis,
      usage: data.usage,
      model: data.model
    })

  } catch (error: any) {
    console.error('视频分析API调用错误:', error)
    
    return NextResponse.json(
      { 
        error: '服务器内部错误',
        details: error.message
      },
      { status: 500 }
    )
  }
}

/**
 * 从文本中解析分析结果（当JSON解析失败时的回退方案）
 */
function parseTextAnalysis(content: string, timestamp: number): any {
  const lines = content.split('\n')
  
  let hasSubtitles = false
  let subtitleText = ''
  let sceneDescription = ''
  let characterActions: string[] = []
  let keyElements: string[] = []
  
  lines.forEach(line => {
    const lowerLine = line.toLowerCase()
    if (lowerLine.includes('字幕') || lowerLine.includes('subtitle')) {
      if (lowerLine.includes('无') || lowerLine.includes('没有') || lowerLine.includes('none')) {
        hasSubtitles = false
      } else {
        hasSubtitles = true
        // 尝试提取字幕文本
        const match = line.match(/[:：]\s*(.+)/)
        if (match) {
          subtitleText = match[1].trim()
        }
      }
    } else if (lowerLine.includes('场景') || lowerLine.includes('scene')) {
      const match = line.match(/[:：]\s*(.+)/)
      if (match) {
        sceneDescription = match[1].trim()
      }
    } else if (lowerLine.includes('行为') || lowerLine.includes('动作') || lowerLine.includes('action')) {
      const match = line.match(/[:：]\s*(.+)/)
      if (match) {
        characterActions = match[1].split(/[,，]/).map(s => s.trim()).filter(s => s)
      }
    }
  })

  return {
    hasSubtitles,
    subtitleText,
    sceneDescription: sceneDescription || '画面内容分析',
    characterActions,
    keyElements,
    confidence: 0.6,
    timestamp
  }
}

/**
 * 验证和补全分析结果
 */
function validateAnalysisResult(analysis: any, timestamp: number): any {
  return {
    hasSubtitles: Boolean(analysis.hasSubtitles),
    subtitleText: analysis.subtitleText || '',
    sceneDescription: analysis.sceneDescription || '画面内容',
    characterActions: Array.isArray(analysis.characterActions) ? analysis.characterActions : [],
    keyElements: Array.isArray(analysis.keyElements) ? analysis.keyElements : [],
    confidence: typeof analysis.confidence === 'number' ? analysis.confidence : 0.5,
    timestamp: timestamp
  }
}
