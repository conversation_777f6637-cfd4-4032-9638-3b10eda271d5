# 硬字幕提取功能说明

## 功能概述

本项目新增了硬字幕提取功能，使用OCR（光学字符识别）技术从视频中提取内嵌的字幕文本，为分集简介生成提供更准确的文本内容。

## 主要特性

### 🔍 智能字幕识别
- 支持中文、英文及中英文混合识别
- 自动检测字幕区域位置
- 可配置的置信度阈值过滤
- 图像预处理增强识别准确率

### 🚀 高效处理
- 基于Tesseract.js的本地OCR引擎
- Web Worker多线程处理，不阻塞主界面
- 智能去重和文本合并算法
- 批量处理优化性能

### 📝 多格式输出
- 标准SRT字幕格式
- 带时间戳的文本格式
- 纯文本内容（用于分集简介）
- 详细的统计信息

### ⚙️ 灵活配置
- 可选择OCR引擎（本地/在线）
- 自定义字幕区域检测
- 可调节的文本处理参数
- 与现有AI分析功能无缝集成

## 技术架构

### 核心组件

1. **SubtitleOCRService** (`utils/subtitle-ocr-service.ts`)
   - OCR引擎管理和配置
   - 字幕文本识别和提取
   - SRT格式生成

2. **SubtitleRegionDetector** (`utils/subtitle-region-detector.ts`)
   - 自动检测字幕区域
   - 多种检测算法组合
   - 置信度评估

3. **SubtitleTextProcessor** (`utils/subtitle-text-processor.ts`)
   - 文本清理和去重
   - 智能合并相似字幕
   - 统计信息生成

4. **SubtitleOCRConfigComponent** (`components/subtitle-ocr-config.tsx`)
   - 用户配置界面
   - 实时参数调整
   - 配置预设管理

### 集成方式

硬字幕提取功能已完全集成到现有的视频分析流程中：

```typescript
// 视频分析配置
const analysisConfig: VideoAnalysisConfig = {
  // 现有配置...
  enableSubtitleOCR: true,        // 启用OCR
  ocrConfig: ocrConfig,           // OCR配置
  useOCROnly: false               // 是否仅使用OCR
}
```

## 使用方法

### 1. 基础使用

1. 在"视频分析设置"中点击"配置硬字幕提取"
2. 启用硬字幕提取功能
3. 选择识别语言（推荐：中英文混合）
4. 调整置信度阈值（推荐：60-80%）
5. 上传视频文件开始分析

### 2. 高级配置

- **OCR引擎选择**：目前支持Tesseract本地引擎
- **图像预处理**：启用可提高识别准确率
- **字幕区域**：支持自动检测或手动设置
- **置信度阈值**：过滤低质量识别结果

### 3. 结果查看

分析完成后可以查看：
- 提取的字幕条目数量
- 平均识别置信度
- 生成的SRT字幕文件
- 用于分集简介的文本内容

## 配置参数说明

### OCR配置 (SubtitleOCRConfig)

```typescript
interface SubtitleOCRConfig {
  enabled: boolean              // 是否启用OCR
  ocrEngine: 'tesseract'        // OCR引擎类型
  language: 'chi_sim+eng'       // 识别语言
  confidence: number            // 置信度阈值 (0.3-0.95)
  preprocessImage: boolean      // 是否预处理图像
  subtitleRegion?: Region       // 字幕区域（可选）
}
```

### 文本处理配置 (TextProcessingConfig)

```typescript
interface TextProcessingConfig {
  similarityThreshold: number   // 文本相似度阈值
  timeWindowSeconds: number     // 时间窗口
  maxGapSeconds: number         // 最大间隔时间
  minDurationSeconds: number    // 最小持续时间
  minTextLength: number         // 最小文本长度
  minConfidence: number         // 最小置信度
}
```

## 性能优化

### 1. 处理速度
- 使用Web Worker避免界面卡顿
- 批量处理减少开销
- 智能缓存避免重复计算

### 2. 内存管理
- 及时清理Canvas资源
- 分批处理大视频文件
- 自动垃圾回收优化

### 3. 准确率提升
- 多种字幕区域检测算法
- 图像预处理增强对比度
- 智能文本清理和纠错

## 常见问题

### Q: OCR识别准确率如何？
A: 对于清晰的中英文字幕，准确率通常在80-95%之间。可通过调整置信度阈值和启用图像预处理来提升效果。

### Q: 支持哪些视频格式？
A: 支持浏览器能够播放的所有视频格式，包括MP4、WebM、AVI等。

### Q: 处理速度如何？
A: 处理速度取决于视频长度和帧提取间隔。通常30分钟视频需要2-5分钟处理时间。

### Q: 是否需要网络连接？
A: 使用Tesseract本地引擎时不需要网络连接，所有处理都在浏览器中完成。

### Q: 如何提高识别准确率？
A: 
- 选择合适的识别语言
- 启用图像预处理
- 调整置信度阈值
- 确保视频字幕清晰可见

## 技术细节

### 字幕区域检测算法
1. **水平线检测**：识别文本的水平特征
2. **底部区域分析**：字幕通常位于视频底部
3. **对比度检测**：字幕与背景的对比度差异
4. **文本区域识别**：综合多种特征的文本区域

### OCR处理流程
1. 视频帧提取
2. 字幕区域检测
3. 图像预处理（可选）
4. OCR文字识别
5. 文本清理和去重
6. 时间轴生成
7. 格式化输出

### 与AI分析的结合
- **互补模式**：OCR提供准确文本，AI提供场景分析
- **纯OCR模式**：仅使用OCR，适合无API密钥场景
- **智能选择**：优先使用OCR结果，AI分析作为补充

## 未来计划

- [ ] 支持更多OCR引擎（百度OCR、腾讯OCR等）
- [ ] 添加字幕样式检测（颜色、字体等）
- [ ] 支持多语言字幕同时识别
- [ ] 优化处理速度和内存使用
- [ ] 添加字幕质量评估功能

## 贡献指南

欢迎提交Issue和Pull Request来改进硬字幕提取功能。请确保：

1. 代码符合项目规范
2. 添加适当的测试用例
3. 更新相关文档
4. 保持向后兼容性

## 许可证

本功能遵循项目的开源许可证。
