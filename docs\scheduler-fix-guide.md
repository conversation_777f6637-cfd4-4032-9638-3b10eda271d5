# 定时任务修复指南

## 问题描述

之前定时任务在执行TMDB导入时经常遇到以下问题：
- TMDB API调用失败，返回500错误
- 网络超时导致任务执行失败
- 错误处理不完善，难以定位问题
- 任务失败后无法自动恢复

## 修复内容

### 1. 添加重试机制
- **文件**: `lib/scheduler.ts`, `lib/tmdb.ts`
- **改进**: 为TMDB API调用添加最多3次重试
- **算法**: 使用指数退避算法（2s, 4s, 8s）避免频繁重试

### 2. 改进超时处理
- **文件**: `app/api/tmdb/recent/route.ts`, `app/api/tmdb/upcoming/route.ts`
- **改进**: 动态调整超时时间，根据重试次数增加超时时长
- **基础超时**: 15秒，每次重试增加5秒

### 3. 增强错误处理
- **文件**: `app/api/execute-tmdb-import/route.ts`, `lib/scheduler.ts`
- **改进**: 
  - 详细的错误分类（网络错误、服务器错误、超时等）
  - 提供具体的错误解决建议
  - 区分可重试和不可重试的错误

### 4. 优化日志记录
- **所有相关文件**
- **改进**: 
  - 添加详细的执行日志
  - 记录重试次数和原因
  - 便于问题排查和监控

## 使用方法

### 1. 重启应用
修复已经应用到代码中，需要重启应用以生效：

```bash
# 停止当前应用
npm run build
npm start
# 或者如果使用开发模式
npm run dev
```

### 2. 监控任务执行
- 打开应用的定时任务管理界面
- 查看任务执行状态和日志
- 关注任务成功率的变化

### 3. 检查日志
在浏览器开发者工具的控制台中查看详细日志：
- `[TaskScheduler]` - 定时任务相关日志
- `[TMDB API]` - API调用相关日志
- `[TMDBService]` - TMDB服务相关日志

## 错误处理说明

### 常见错误类型及处理

1. **网络连接错误**
   - 错误信息：包含 `network`, `ENOTFOUND`, `ECONNREFUSED`
   - 处理：自动重试，建议检查网络设置

2. **服务器错误 (500)**
   - 错误信息：`API请求失败 (500)`
   - 处理：自动重试，如持续失败建议稍后再试

3. **请求超时**
   - 错误信息：`AbortError` 或包含 `timeout`
   - 处理：自动重试，超时时间会逐步增加

4. **API限流 (429)**
   - 错误信息：`API请求失败 (429)`
   - 处理：等待指定时间后重试

5. **认证错误 (401)**
   - 错误信息：`API请求失败 (401)`
   - 处理：不重试，需要检查API密钥

## 监控建议

### 1. 定期检查
- 每天查看定时任务执行情况
- 关注失败任务的错误信息
- 监控任务执行时间是否正常

### 2. 性能指标
- 任务成功率应该 > 90%
- 平均执行时间应该稳定
- 重试次数应该较少

### 3. 告警设置
建议在以下情况下设置告警：
- 连续3次任务执行失败
- 任务执行时间超过预期
- API调用成功率低于80%

## 故障排除

### 如果任务仍然失败

1. **检查网络连接**
   ```bash
   # 测试TMDB API连接
   curl -I "https://api.themoviedb.org/3"
   ```

2. **验证API密钥**
   - 确保TMDB API密钥有效
   - 检查API配额是否用完

3. **查看详细日志**
   - 打开浏览器开发者工具
   - 查看Console标签页的错误信息
   - 记录错误的完整堆栈信息

4. **手动测试**
   - 尝试手动执行相同的操作
   - 确认问题是否只在定时任务中出现

### 联系支持

如果问题持续存在，请提供以下信息：
- 错误的完整日志
- 任务配置信息
- 网络环境描述
- 问题出现的时间和频率

## 更新日志

- **2025-01-06**: 初始修复版本
  - 添加重试机制
  - 改进超时处理
  - 增强错误处理
  - 优化日志记录

---

*此文档会根据用户反馈和问题修复情况持续更新*
