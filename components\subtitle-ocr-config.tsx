'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Info, Settings, Eye, Zap, Globe } from 'lucide-react'
import { SubtitleOCRConfig, DEFAULT_OCR_CONFIG } from '@/utils/subtitle-ocr-service'

interface SubtitleOCRConfigProps {
  config: SubtitleOCRConfig
  onConfigChange: (config: SubtitleOCRConfig) => void
  disabled?: boolean
}

export function SubtitleOCRConfigComponent({ 
  config, 
  onConfigChange, 
  disabled = false 
}: SubtitleOCRConfigProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const handleConfigChange = (updates: Partial<SubtitleOCRConfig>) => {
    const newConfig = { ...config, ...updates }
    onConfigChange(newConfig)
  }

  const resetToDefaults = () => {
    onConfigChange(DEFAULT_OCR_CONFIG)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Eye className="h-5 w-5 text-blue-500" />
            <CardTitle>硬字幕提取配置</CardTitle>
            <Badge variant={config.enabled ? "default" : "secondary"}>
              {config.enabled ? "已启用" : "已禁用"}
            </Badge>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            disabled={disabled}
          >
            <Settings className="h-4 w-4 mr-1" />
            {showAdvanced ? "隐藏高级选项" : "显示高级选项"}
          </Button>
        </div>
        <CardDescription>
          使用OCR技术从视频中提取硬字幕，为分集简介提供更准确的文本内容
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 基础配置 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="enable-ocr">启用硬字幕提取</Label>
              <p className="text-sm text-muted-foreground">
                开启后将使用OCR技术提取视频中的字幕文本
              </p>
            </div>
            <Switch
              id="enable-ocr"
              checked={config.enabled}
              onCheckedChange={(enabled) => handleConfigChange({ enabled })}
              disabled={disabled}
            />
          </div>

          {config.enabled && (
            <>
              <Separator />
              
              <div className="space-y-2">
                <Label>OCR引擎</Label>
                <Select
                  value={config.ocrEngine}
                  onValueChange={(value: 'tesseract' | 'online-api') => 
                    handleConfigChange({ ocrEngine: value })
                  }
                  disabled={disabled}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="tesseract">
                      <div className="flex items-center space-x-2">
                        <Zap className="h-4 w-4" />
                        <span>Tesseract (本地)</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="online-api">
                      <div className="flex items-center space-x-2">
                        <Globe className="h-4 w-4" />
                        <span>在线API (暂未实现)</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  推荐使用Tesseract本地引擎，无需网络连接且免费
                </p>
              </div>

              <div className="space-y-2">
                <Label>识别语言</Label>
                <Select
                  value={config.language}
                  onValueChange={(value: 'chi_sim' | 'eng' | 'chi_sim+eng') => 
                    handleConfigChange({ language: value })
                  }
                  disabled={disabled}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chi_sim">简体中文</SelectItem>
                    <SelectItem value="eng">英文</SelectItem>
                    <SelectItem value="chi_sim+eng">中英文混合</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>置信度阈值</Label>
                  <span className="text-sm text-muted-foreground">
                    {Math.round(config.confidence * 100)}%
                  </span>
                </div>
                <Slider
                  value={[config.confidence]}
                  onValueChange={([value]) => handleConfigChange({ confidence: value })}
                  min={0.3}
                  max={0.95}
                  step={0.05}
                  disabled={disabled}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  只保留置信度高于此阈值的识别结果
                </p>
              </div>
            </>
          )}
        </div>

        {/* 高级配置 */}
        {config.enabled && showAdvanced && (
          <>
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Info className="h-4 w-4 text-blue-500" />
                <Label className="text-sm font-medium">高级选项</Label>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="preprocess-image">图像预处理</Label>
                  <p className="text-sm text-muted-foreground">
                    对图像进行二值化处理以提高OCR准确率
                  </p>
                </div>
                <Switch
                  id="preprocess-image"
                  checked={config.preprocessImage}
                  onCheckedChange={(preprocessImage) => 
                    handleConfigChange({ preprocessImage })
                  }
                  disabled={disabled}
                />
              </div>

              {config.subtitleRegion && (
                <div className="space-y-2">
                  <Label>字幕区域设置</Label>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <Label className="text-xs">X: {config.subtitleRegion.x}</Label>
                    </div>
                    <div>
                      <Label className="text-xs">Y: {config.subtitleRegion.y}</Label>
                    </div>
                    <div>
                      <Label className="text-xs">宽度: {config.subtitleRegion.width}</Label>
                    </div>
                    <div>
                      <Label className="text-xs">高度: {config.subtitleRegion.height}</Label>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleConfigChange({ subtitleRegion: undefined })}
                    disabled={disabled}
                  >
                    重置为自动检测
                  </Button>
                </div>
              )}
            </div>
          </>
        )}

        {/* 操作按钮 */}
        {config.enabled && (
          <>
            <Separator />
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={resetToDefaults}
                disabled={disabled}
              >
                恢复默认设置
              </Button>
              
              <div className="text-xs text-muted-foreground">
                配置将自动保存
              </div>
            </div>
          </>
        )}

        {/* 帮助信息 */}
        <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
          <div className="flex items-start space-x-2">
            <Info className="h-4 w-4 text-blue-500 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-blue-700 dark:text-blue-300 mb-1">
                使用提示
              </p>
              <ul className="text-blue-600 dark:text-blue-400 space-y-1 text-xs">
                <li>• 硬字幕提取适用于内嵌在视频中的字幕</li>
                <li>• 中英文混合模式适合大多数视频内容</li>
                <li>• 较高的置信度阈值可以减少错误识别</li>
                <li>• 图像预处理可以提高识别准确率但可能增加处理时间</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default SubtitleOCRConfigComponent
