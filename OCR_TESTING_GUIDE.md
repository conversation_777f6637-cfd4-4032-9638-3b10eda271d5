# 硬字幕提取功能测试指南

## 🎯 测试目标

验证新增的硬字幕提取功能是否正常工作，包括：
- OCR配置界面
- 视频字幕提取
- 文本处理和去重
- 与分集简介生成的集成

## 🚀 快速开始

### 1. 启动应用
```bash
npm run dev
```
应用将在 http://localhost:3001 启动

### 2. 访问功能
1. 打开浏览器访问 http://localhost:3001
2. 进入"分集简介生成器"页面
3. 点击右上角的"设置"按钮
4. 切换到"视频分析设置"标签页

## 📋 测试步骤

### 步骤1：配置OCR设置

1. **打开OCR配置**
   - 在"视频分析设置"中找到"硬字幕提取"卡片
   - 点击"配置"按钮打开OCR配置对话框

2. **验证配置选项**
   - ✅ 启用/禁用开关
   - ✅ OCR引擎选择（Tesseract本地）
   - ✅ 识别语言选择（中文/英文/中英文混合）
   - ✅ 置信度阈值滑块（30%-95%）
   - ✅ 图像预处理开关
   - ✅ 高级选项展开/收起

3. **测试配置保存**
   - 修改配置参数
   - 关闭对话框
   - 重新打开验证配置是否保存

### 步骤2：测试视频分析

1. **准备测试视频**
   - 使用包含中文或英文字幕的视频文件
   - 推荐格式：MP4, WebM
   - 建议时长：1-5分钟（测试用）

2. **上传视频**
   - 拖拽视频文件到上传区域
   - 或点击选择文件

3. **配置分析参数**
   - 帧提取间隔：建议30-60秒
   - 启用硬字幕提取
   - 设置合适的置信度阈值

4. **开始分析**
   - 点击"开始视频分析"
   - 观察进度显示：
     - ✅ 视频信息获取
     - ✅ 关键帧提取
     - ✅ OCR引擎初始化
     - ✅ 帧分析和字幕提取
     - ✅ 结果整合

### 步骤3：验证结果

1. **检查分析结果**
   - 查看完成提示中的统计信息
   - 验证OCR统计数据：
     - 处理帧数
     - 成功提取数
     - 平均置信度
     - 字幕条目数

2. **查看生成内容**
   - 检查分集简介是否包含提取的字幕内容
   - 验证文本质量和准确性
   - 确认时间轴信息正确

## 🔍 测试重点

### 功能测试
- [ ] OCR配置界面正常显示和操作
- [ ] 配置参数正确保存和加载
- [ ] 视频文件上传和识别
- [ ] OCR引擎初始化成功
- [ ] 字幕提取过程正常
- [ ] 结果统计信息准确

### 性能测试
- [ ] 处理速度合理（不超过视频时长的2倍）
- [ ] 内存使用稳定
- [ ] 界面响应流畅
- [ ] 错误处理正确

### 兼容性测试
- [ ] 不同视频格式支持
- [ ] 中英文字幕识别
- [ ] 各种字幕位置（顶部/底部）
- [ ] 不同分辨率视频

## 🐛 常见问题排查

### 问题1：OCR引擎初始化失败
**症状**：显示"OCR引擎初始化失败"错误
**解决**：
1. 检查网络连接（首次需要下载语言包）
2. 清除浏览器缓存
3. 尝试刷新页面

### 问题2：字幕识别准确率低
**症状**：提取的字幕文本错误较多
**解决**：
1. 提高置信度阈值（70-80%）
2. 启用图像预处理
3. 确保视频字幕清晰可见
4. 选择正确的识别语言

### 问题3：处理速度慢
**症状**：视频分析耗时过长
**解决**：
1. 增大帧提取间隔（60-120秒）
2. 使用较短的测试视频
3. 关闭其他占用资源的应用

### 问题4：内存不足
**症状**：浏览器卡顿或崩溃
**解决**：
1. 使用较小的视频文件
2. 降低图像质量设置
3. 重启浏览器

## 📊 测试数据记录

### 测试环境
- 浏览器：_____________
- 操作系统：___________
- 内存：_______________
- 网络状况：___________

### 测试结果
| 测试项目 | 状态 | 备注 |
|---------|------|------|
| OCR配置界面 | ⭕ | |
| 视频上传 | ⭕ | |
| 字幕提取 | ⭕ | |
| 结果显示 | ⭕ | |
| 性能表现 | ⭕ | |

### 发现的问题
1. ________________________________
2. ________________________________
3. ________________________________

## 🎉 测试完成检查清单

- [ ] 所有核心功能正常工作
- [ ] 配置保存和加载正确
- [ ] 错误处理机制有效
- [ ] 用户界面友好易用
- [ ] 性能表现满足要求
- [ ] 文档说明清晰准确

## 📞 反馈渠道

如果在测试过程中发现问题或有改进建议，请：

1. 记录详细的错误信息和复现步骤
2. 截图保存错误界面
3. 提供测试视频的基本信息（格式、时长、字幕类型）
4. 描述期望的行为和实际行为差异

## 🔄 后续优化

基于测试结果，可能的优化方向：

1. **准确率提升**
   - 优化字幕区域检测算法
   - 改进图像预处理效果
   - 添加更多语言支持

2. **性能优化**
   - 实现增量处理
   - 优化内存使用
   - 添加处理进度细节

3. **用户体验**
   - 添加预览功能
   - 改进错误提示
   - 增加使用指南

感谢您的测试和反馈！🙏
