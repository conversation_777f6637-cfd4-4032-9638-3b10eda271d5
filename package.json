{"name": "tmdb-helper", "version": "0.3.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "task-daemon": "node scripts/task-scheduler-daemon.js", "task-daemon:dev": "node scripts/task-scheduler-daemon.js --port=4949 --interval=300"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.0.5", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.0.7", "@radix-ui/react-progress": "1.0.3", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.0.5", "@radix-ui/react-select": "2.1.1", "@radix-ui/react-separator": "1.0.3", "@radix-ui/react-slider": "1.1.2", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-switch": "1.0.3", "@radix-ui/react-tabs": "1.0.4", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@radix-ui/react-visually-hidden": "1.2.3", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.10", "@types/bcryptjs": "^2.4.6", "@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.10", "@types/uuid": "^10.0.0", "antd": "^5.26.0", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "child_process": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "csv-parse": "^5.6.0", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "file-saver": "^2.0.5", "formidable": "^3.5.4", "fs": "latest", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lucide-react": "^0.522.0", "next": "15.2.4", "next-themes": "0.3.0", "papaparse": "^5.5.3", "path": "latest", "rc-util": "^5.38.1", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.12.4", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^6.0.1", "use-debounce": "^10.0.5", "uuid": "^11.1.0", "vaul": "^0.9.6", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}