import { createWorker, Worker } from 'tesseract.js'

// OCR配置接口
export interface SubtitleOCRConfig {
  enabled: boolean
  ocrEngine: 'tesseract' | 'online-api'
  language: 'chi_sim' | 'eng' | 'chi_sim+eng'
  subtitleRegion?: {
    x: number
    y: number
    width: number
    height: number
  }
  confidence: number
  preprocessImage: boolean
}

// OCR结果接口
export interface OCRResult {
  text: string
  confidence: number
  timestamp: number
  boundingBox?: {
    x: number
    y: number
    width: number
    height: number
  }
}

// 字幕帧接口
export interface SubtitleFrame {
  timestamp: number
  imageData: ImageData
  canvas: HTMLCanvasElement
  ocrResult?: OCRResult
}

// 默认配置
export const DEFAULT_OCR_CONFIG: SubtitleOCRConfig = {
  enabled: true,
  ocrEngine: 'tesseract',
  language: 'chi_sim+eng',
  confidence: 0.6,
  preprocessImage: true,
  subtitleRegion: undefined // 自动检测
}

/**
 * 硬字幕提取服务
 */
export class SubtitleOCRService {
  private worker: Worker | null = null
  private config: SubtitleOCRConfig
  private isInitialized = false

  constructor(config: SubtitleOCRConfig = DEFAULT_OCR_CONFIG) {
    this.config = { ...DEFAULT_OCR_CONFIG, ...config }
  }

  /**
   * 初始化OCR引擎
   */
  async initialize(onProgress?: (progress: number, status: string) => void): Promise<void> {
    if (this.isInitialized) return

    try {
      onProgress?.(0, '初始化OCR引擎...')
      
      this.worker = await createWorker(this.config.language, 1, {
        logger: (m) => {
          if (m.status === 'recognizing text') {
            const progress = Math.round(m.progress * 100)
            onProgress?.(progress, `OCR识别中: ${progress}%`)
          }
        }
      })

      // 设置OCR参数
      await this.worker.setParameters({
        tessedit_char_whitelist: '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz一二三四五六七八九十百千万亿零壹贰叁肆伍陆柒捌玖拾佰仟萬億',
        tessedit_pageseg_mode: '6', // 单一文本块
        tessedit_ocr_engine_mode: '1' // LSTM引擎
      })

      this.isInitialized = true
      onProgress?.(100, 'OCR引擎初始化完成')
    } catch (error) {
      console.error('OCR引擎初始化失败:', error)
      throw new Error(`OCR引擎初始化失败: ${error}`)
    }
  }

  /**
   * 检测字幕区域
   */
  detectSubtitleRegion(canvas: HTMLCanvasElement): { x: number, y: number, width: number, height: number } {
    const ctx = canvas.getContext('2d')!
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    
    // 默认检测底部区域（通常字幕在这里）
    const height = Math.floor(canvas.height * 0.25) // 底部25%区域
    const y = canvas.height - height
    
    return {
      x: 0,
      y: y,
      width: canvas.width,
      height: height
    }
  }

  /**
   * 图像预处理
   */
  preprocessImage(canvas: HTMLCanvasElement, region?: { x: number, y: number, width: number, height: number }): HTMLCanvasElement {
    const ctx = canvas.getContext('2d')!
    
    // 确定处理区域
    const processRegion = region || this.config.subtitleRegion || this.detectSubtitleRegion(canvas)
    
    // 创建新的canvas用于处理
    const processCanvas = document.createElement('canvas')
    processCanvas.width = processRegion.width
    processCanvas.height = processRegion.height
    const processCtx = processCanvas.getContext('2d')!
    
    // 提取字幕区域
    const imageData = ctx.getImageData(
      processRegion.x,
      processRegion.y,
      processRegion.width,
      processRegion.height
    )
    
    if (this.config.preprocessImage) {
      // 图像预处理：增强对比度、去噪等
      this.enhanceImageForOCR(imageData)
    }
    
    processCtx.putImageData(imageData, 0, 0)
    return processCanvas
  }

  /**
   * 增强图像用于OCR识别
   */
  private enhanceImageForOCR(imageData: ImageData): void {
    const data = imageData.data
    
    // 转换为灰度并增强对比度
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]
      
      // 灰度转换
      const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b)
      
      // 二值化处理（增强文字对比度）
      const threshold = 128
      const binary = gray > threshold ? 255 : 0
      
      data[i] = binary     // R
      data[i + 1] = binary // G
      data[i + 2] = binary // B
      // Alpha保持不变
    }
  }

  /**
   * 提取单帧字幕
   */
  async extractSubtitleFromFrame(
    canvas: HTMLCanvasElement,
    timestamp: number,
    onProgress?: (progress: number, status: string) => void
  ): Promise<OCRResult | null> {
    if (!this.isInitialized) {
      await this.initialize(onProgress)
    }

    try {
      onProgress?.(0, `处理帧 ${timestamp.toFixed(1)}s`)
      
      // 预处理图像
      const processedCanvas = this.preprocessImage(canvas)
      
      onProgress?.(30, '开始OCR识别...')
      
      // 执行OCR识别
      const { data } = await this.worker!.recognize(processedCanvas)
      
      // 过滤低置信度结果
      if (data.confidence < this.config.confidence * 100) {
        return null
      }

      // 清理识别文本
      const cleanText = this.cleanOCRText(data.text)
      if (!cleanText || cleanText.length < 2) {
        return null
      }

      onProgress?.(100, '识别完成')

      return {
        text: cleanText,
        confidence: data.confidence / 100,
        timestamp: timestamp,
        boundingBox: data.bbox ? {
          x: data.bbox.x0,
          y: data.bbox.y0,
          width: data.bbox.x1 - data.bbox.x0,
          height: data.bbox.y1 - data.bbox.y0
        } : undefined
      }
    } catch (error) {
      console.error('OCR识别失败:', error)
      return null
    }
  }

  /**
   * 清理OCR识别的文本
   */
  private cleanOCRText(text: string): string {
    return text
      .replace(/\n+/g, ' ') // 替换换行符为空格
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s.,!?;:'"()（）。，！？；：""'']/g, '') // 保留中英文、数字和常用标点
      .trim()
  }

  /**
   * 批量提取字幕
   */
  async extractSubtitlesFromFrames(
    frames: SubtitleFrame[],
    onProgress?: (progress: number, status: string, currentFrame?: number) => void
  ): Promise<OCRResult[]> {
    const results: OCRResult[] = []
    
    for (let i = 0; i < frames.length; i++) {
      const frame = frames[i]
      const progress = (i / frames.length) * 100
      
      onProgress?.(progress, `处理第 ${i + 1}/${frames.length} 帧`, i)
      
      try {
        const result = await this.extractSubtitleFromFrame(frame.canvas, frame.timestamp)
        if (result) {
          results.push(result)
        }
      } catch (error) {
        console.warn(`帧 ${i} OCR处理失败:`, error)
      }
      
      // 避免过度占用资源
      if (i < frames.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    onProgress?.(100, '字幕提取完成')
    return this.deduplicateSubtitles(results)
  }

  /**
   * 去重和合并相似字幕
   */
  private deduplicateSubtitles(results: OCRResult[]): OCRResult[] {
    if (results.length === 0) return []
    
    const deduplicated: OCRResult[] = []
    let lastResult: OCRResult | null = null
    
    for (const result of results) {
      if (!lastResult) {
        deduplicated.push(result)
        lastResult = result
        continue
      }
      
      // 计算文本相似度
      const similarity = this.calculateTextSimilarity(lastResult.text, result.text)
      const timeDiff = result.timestamp - lastResult.timestamp
      
      // 如果文本相似且时间间隔较短，则跳过
      if (similarity > 0.8 && timeDiff < 2) {
        continue
      }
      
      deduplicated.push(result)
      lastResult = result
    }
    
    return deduplicated
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    if (text1 === text2) return 1
    if (!text1 || !text2) return 0
    
    const longer = text1.length > text2.length ? text1 : text2
    const shorter = text1.length > text2.length ? text2 : text1
    
    if (longer.length === 0) return 1
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  /**
   * 生成SRT格式字幕
   */
  generateSRTSubtitle(results: OCRResult[]): string {
    const srtLines: string[] = []
    
    results.forEach((result, index) => {
      const startTime = this.formatSRTTime(result.timestamp)
      const endTime = this.formatSRTTime(result.timestamp + 2) // 假设每条字幕显示2秒
      
      srtLines.push(`${index + 1}`)
      srtLines.push(`${startTime} --> ${endTime}`)
      srtLines.push(result.text)
      srtLines.push('')
    })
    
    return srtLines.join('\n')
  }

  /**
   * 格式化SRT时间
   */
  private formatSRTTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate()
      this.worker = null
    }
    this.isInitialized = false
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<SubtitleOCRConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}
