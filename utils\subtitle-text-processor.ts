import { OCRResult } from './subtitle-ocr-service'

/**
 * 处理后的字幕条目
 */
export interface ProcessedSubtitle {
  id: string
  text: string
  startTime: number
  endTime: number
  confidence: number
  originalResults: OCRResult[]
}

/**
 * 文本处理配置
 */
export interface TextProcessingConfig {
  // 去重配置
  similarityThreshold: number    // 文本相似度阈值
  timeWindowSeconds: number      // 时间窗口（秒）
  
  // 合并配置
  maxGapSeconds: number          // 最大间隔时间
  minDurationSeconds: number     // 最小持续时间
  maxDurationSeconds: number     // 最大持续时间
  
  // 文本清理配置
  minTextLength: number          // 最小文本长度
  maxTextLength: number          // 最大文本长度
  removeSpecialChars: boolean    // 移除特殊字符
  
  // 置信度配置
  minConfidence: number          // 最小置信度
  confidenceBoostForLength: boolean // 根据长度提升置信度
}

export const DEFAULT_PROCESSING_CONFIG: TextProcessingConfig = {
  similarityThreshold: 0.8,
  timeWindowSeconds: 3.0,
  maxGapSeconds: 1.5,
  minDurationSeconds: 1.0,
  maxDurationSeconds: 10.0,
  minTextLength: 2,
  maxTextLength: 200,
  removeSpecialChars: true,
  minConfidence: 0.6,
  confidenceBoostForLength: true
}

/**
 * 字幕文本后处理器
 */
export class SubtitleTextProcessor {
  private config: TextProcessingConfig

  constructor(config: TextProcessingConfig = DEFAULT_PROCESSING_CONFIG) {
    this.config = { ...DEFAULT_PROCESSING_CONFIG, ...config }
  }

  /**
   * 处理OCR结果，生成清理后的字幕
   */
  processOCRResults(ocrResults: OCRResult[]): ProcessedSubtitle[] {
    if (ocrResults.length === 0) return []

    // 1. 预处理：清理和过滤
    const cleanedResults = this.cleanAndFilterResults(ocrResults)
    
    // 2. 去重：移除重复和相似的字幕
    const deduplicatedResults = this.deduplicateResults(cleanedResults)
    
    // 3. 合并：合并相近的字幕条目
    const mergedResults = this.mergeNearbySubtitles(deduplicatedResults)
    
    // 4. 生成最终字幕条目
    const processedSubtitles = this.generateSubtitleEntries(mergedResults)
    
    // 5. 后处理：调整时间轴和置信度
    return this.postProcessSubtitles(processedSubtitles)
  }

  /**
   * 清理和过滤OCR结果
   */
  private cleanAndFilterResults(results: OCRResult[]): OCRResult[] {
    return results
      .map(result => ({
        ...result,
        text: this.cleanText(result.text)
      }))
      .filter(result => this.isValidResult(result))
      .sort((a, b) => a.timestamp - b.timestamp)
  }

  /**
   * 清理文本
   */
  private cleanText(text: string): string {
    let cleaned = text
    
    // 移除多余的空白字符
    cleaned = cleaned.replace(/\s+/g, ' ').trim()
    
    // 移除特殊字符（如果启用）
    if (this.config.removeSpecialChars) {
      // 保留中英文、数字和常用标点
      cleaned = cleaned.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s.,!?;:'"()（）。，！？；：""'']/g, '')
    }
    
    // 修复常见OCR错误
    cleaned = this.fixCommonOCRErrors(cleaned)
    
    return cleaned.trim()
  }

  /**
   * 修复常见OCR错误
   */
  private fixCommonOCRErrors(text: string): string {
    const corrections: { [key: string]: string } = {
      // 数字错误
      'O': '0',
      'l': '1',
      'I': '1',
      'S': '5',
      'Z': '2',
      
      // 中文常见错误
      '囗': '口',
      '囝': '囚',
      '囱': '窗',
      
      // 英文常见错误
      'rn': 'm',
      'cl': 'd',
      'vv': 'w'
    }
    
    let corrected = text
    for (const [wrong, right] of Object.entries(corrections)) {
      corrected = corrected.replace(new RegExp(wrong, 'g'), right)
    }
    
    return corrected
  }

  /**
   * 验证结果是否有效
   */
  private isValidResult(result: OCRResult): boolean {
    // 检查文本长度
    if (result.text.length < this.config.minTextLength || 
        result.text.length > this.config.maxTextLength) {
      return false
    }
    
    // 检查置信度
    if (result.confidence < this.config.minConfidence) {
      return false
    }
    
    // 检查是否为纯空白或特殊字符
    if (!/[\u4e00-\u9fa5a-zA-Z0-9]/.test(result.text)) {
      return false
    }
    
    return true
  }

  /**
   * 去重处理
   */
  private deduplicateResults(results: OCRResult[]): OCRResult[] {
    const deduplicated: OCRResult[] = []
    
    for (const current of results) {
      let isDuplicate = false
      
      // 检查是否与已有结果重复
      for (const existing of deduplicated) {
        const timeDiff = Math.abs(current.timestamp - existing.timestamp)
        const similarity = this.calculateTextSimilarity(current.text, existing.text)
        
        if (timeDiff <= this.config.timeWindowSeconds && similarity >= this.config.similarityThreshold) {
          isDuplicate = true
          
          // 如果当前结果置信度更高，替换现有结果
          if (current.confidence > existing.confidence) {
            const index = deduplicated.indexOf(existing)
            deduplicated[index] = current
          }
          break
        }
      }
      
      if (!isDuplicate) {
        deduplicated.push(current)
      }
    }
    
    return deduplicated.sort((a, b) => a.timestamp - b.timestamp)
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    if (text1 === text2) return 1.0
    if (!text1 || !text2) return 0.0
    
    // 使用Jaccard相似度
    const set1 = new Set(text1.split(''))
    const set2 = new Set(text2.split(''))
    
    const intersection = new Set([...set1].filter(x => set2.has(x)))
    const union = new Set([...set1, ...set2])
    
    return intersection.size / union.size
  }

  /**
   * 合并相近的字幕
   */
  private mergeNearbySubtitles(results: OCRResult[]): OCRResult[] {
    if (results.length <= 1) return results
    
    const merged: OCRResult[] = []
    let currentGroup: OCRResult[] = [results[0]]
    
    for (let i = 1; i < results.length; i++) {
      const current = results[i]
      const lastInGroup = currentGroup[currentGroup.length - 1]
      
      const timeDiff = current.timestamp - lastInGroup.timestamp
      const textSimilarity = this.calculateTextSimilarity(current.text, lastInGroup.text)
      
      // 如果时间间隔小且文本相似，加入当前组
      if (timeDiff <= this.config.maxGapSeconds && textSimilarity > 0.3) {
        currentGroup.push(current)
      } else {
        // 处理当前组并开始新组
        if (currentGroup.length > 0) {
          merged.push(this.mergeGroup(currentGroup))
        }
        currentGroup = [current]
      }
    }
    
    // 处理最后一组
    if (currentGroup.length > 0) {
      merged.push(this.mergeGroup(currentGroup))
    }
    
    return merged
  }

  /**
   * 合并一组OCR结果
   */
  private mergeGroup(group: OCRResult[]): OCRResult {
    if (group.length === 1) return group[0]
    
    // 选择置信度最高的文本
    const bestResult = group.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    )
    
    // 计算平均时间戳
    const avgTimestamp = group.reduce((sum, result) => sum + result.timestamp, 0) / group.length
    
    // 计算平均置信度
    const avgConfidence = group.reduce((sum, result) => sum + result.confidence, 0) / group.length
    
    return {
      text: bestResult.text,
      confidence: avgConfidence,
      timestamp: avgTimestamp,
      boundingBox: bestResult.boundingBox
    }
  }

  /**
   * 生成字幕条目
   */
  private generateSubtitleEntries(results: OCRResult[]): ProcessedSubtitle[] {
    const subtitles: ProcessedSubtitle[] = []
    
    for (let i = 0; i < results.length; i++) {
      const current = results[i]
      const next = results[i + 1]
      
      // 计算结束时间
      let endTime: number
      if (next) {
        // 如果有下一条字幕，结束时间为下一条开始时间的前一点
        endTime = Math.min(
          current.timestamp + this.config.maxDurationSeconds,
          next.timestamp - 0.1
        )
      } else {
        // 最后一条字幕，使用默认持续时间
        endTime = current.timestamp + Math.min(this.config.maxDurationSeconds, 3.0)
      }
      
      // 确保最小持续时间
      endTime = Math.max(endTime, current.timestamp + this.config.minDurationSeconds)
      
      subtitles.push({
        id: `subtitle_${i}_${Date.now()}`,
        text: current.text,
        startTime: current.timestamp,
        endTime: endTime,
        confidence: current.confidence,
        originalResults: [current]
      })
    }
    
    return subtitles
  }

  /**
   * 后处理字幕
   */
  private postProcessSubtitles(subtitles: ProcessedSubtitle[]): ProcessedSubtitle[] {
    return subtitles.map(subtitle => {
      let adjustedConfidence = subtitle.confidence
      
      // 根据文本长度调整置信度
      if (this.config.confidenceBoostForLength) {
        const lengthBoost = Math.min(subtitle.text.length / 20, 0.2)
        adjustedConfidence = Math.min(adjustedConfidence + lengthBoost, 1.0)
      }
      
      return {
        ...subtitle,
        confidence: adjustedConfidence
      }
    })
  }

  /**
   * 生成SRT格式字幕
   */
  generateSRT(subtitles: ProcessedSubtitle[]): string {
    const srtLines: string[] = []
    
    subtitles.forEach((subtitle, index) => {
      const startTime = this.formatSRTTime(subtitle.startTime)
      const endTime = this.formatSRTTime(subtitle.endTime)
      
      srtLines.push(`${index + 1}`)
      srtLines.push(`${startTime} --> ${endTime}`)
      srtLines.push(subtitle.text)
      srtLines.push('')
    })
    
    return srtLines.join('\n')
  }

  /**
   * 格式化SRT时间
   */
  private formatSRTTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.floor((seconds % 1) * 1000)
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
  }

  /**
   * 生成纯文本内容（用于分集简介）
   */
  generatePlainText(subtitles: ProcessedSubtitle[]): string {
    return subtitles
      .filter(subtitle => subtitle.confidence > 0.7) // 只使用高置信度字幕
      .map(subtitle => subtitle.text)
      .join(' ')
  }

  /**
   * 生成带时间戳的文本（用于分集简介）
   */
  generateTimestampedText(subtitles: ProcessedSubtitle[]): string {
    return subtitles
      .filter(subtitle => subtitle.confidence > 0.7)
      .map(subtitle => {
        const timeStr = this.formatTime(subtitle.startTime)
        return `[${timeStr}] ${subtitle.text}`
      })
      .join('\n')
  }

  /**
   * 格式化时间显示
   */
  private formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  /**
   * 获取统计信息
   */
  getStatistics(subtitles: ProcessedSubtitle[]): {
    totalSubtitles: number
    averageConfidence: number
    totalDuration: number
    averageLength: number
    highConfidenceCount: number
  } {
    if (subtitles.length === 0) {
      return {
        totalSubtitles: 0,
        averageConfidence: 0,
        totalDuration: 0,
        averageLength: 0,
        highConfidenceCount: 0
      }
    }
    
    const totalConfidence = subtitles.reduce((sum, sub) => sum + sub.confidence, 0)
    const totalLength = subtitles.reduce((sum, sub) => sum + sub.text.length, 0)
    const totalDuration = subtitles.reduce((sum, sub) => sum + (sub.endTime - sub.startTime), 0)
    const highConfidenceCount = subtitles.filter(sub => sub.confidence > 0.8).length
    
    return {
      totalSubtitles: subtitles.length,
      averageConfidence: totalConfidence / subtitles.length,
      totalDuration: totalDuration,
      averageLength: totalLength / subtitles.length,
      highConfidenceCount: highConfidenceCount
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<TextProcessingConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}
