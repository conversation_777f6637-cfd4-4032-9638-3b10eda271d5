/**
 * 字幕区域检测器
 * 用于自动检测视频帧中的字幕位置
 */

export interface SubtitleRegion {
  x: number
  y: number
  width: number
  height: number
  confidence: number
}

export interface DetectionConfig {
  // 检测区域配置
  searchBottomRatio: number // 在底部多少比例的区域搜索字幕
  searchTopRatio: number    // 在顶部多少比例的区域搜索字幕
  minTextHeight: number     // 最小文本高度
  maxTextHeight: number     // 最大文本高度
  
  // 文本检测参数
  contrastThreshold: number // 对比度阈值
  edgeThreshold: number     // 边缘检测阈值
  minTextWidth: number      // 最小文本宽度
  
  // 字幕特征参数
  horizontalLineWeight: number // 水平线权重
  bottomAreaWeight: number     // 底部区域权重
  contrastWeight: number       // 对比度权重
}

export const DEFAULT_DETECTION_CONFIG: DetectionConfig = {
  searchBottomRatio: 0.3,
  searchTopRatio: 0.15,
  minTextHeight: 20,
  maxTextHeight: 80,
  contrastThreshold: 50,
  edgeThreshold: 100,
  minTextWidth: 100,
  horizontalLineWeight: 0.4,
  bottomAreaWeight: 0.4,
  contrastWeight: 0.2
}

export class SubtitleRegionDetector {
  private config: DetectionConfig

  constructor(config: DetectionConfig = DEFAULT_DETECTION_CONFIG) {
    this.config = { ...DEFAULT_DETECTION_CONFIG, ...config }
  }

  /**
   * 检测字幕区域
   */
  detectSubtitleRegions(canvas: HTMLCanvasElement): SubtitleRegion[] {
    const ctx = canvas.getContext('2d')!
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    
    const regions: SubtitleRegion[] = []
    
    // 检测底部字幕区域
    const bottomRegions = this.detectBottomSubtitles(imageData, canvas.width, canvas.height)
    regions.push(...bottomRegions)
    
    // 检测顶部字幕区域
    const topRegions = this.detectTopSubtitles(imageData, canvas.width, canvas.height)
    regions.push(...topRegions)
    
    // 按置信度排序
    regions.sort((a, b) => b.confidence - a.confidence)
    
    return regions
  }

  /**
   * 检测底部字幕
   */
  private detectBottomSubtitles(imageData: ImageData, width: number, height: number): SubtitleRegion[] {
    const searchHeight = Math.floor(height * this.config.searchBottomRatio)
    const searchY = height - searchHeight
    
    return this.detectTextRegionsInArea(imageData, width, height, 0, searchY, width, searchHeight, 'bottom')
  }

  /**
   * 检测顶部字幕
   */
  private detectTopSubtitles(imageData: ImageData, width: number, height: number): SubtitleRegion[] {
    const searchHeight = Math.floor(height * this.config.searchTopRatio)
    
    return this.detectTextRegionsInArea(imageData, width, height, 0, 0, width, searchHeight, 'top')
  }

  /**
   * 在指定区域检测文本区域
   */
  private detectTextRegionsInArea(
    imageData: ImageData,
    canvasWidth: number,
    canvasHeight: number,
    x: number,
    y: number,
    width: number,
    height: number,
    position: 'top' | 'bottom'
  ): SubtitleRegion[] {
    const regions: SubtitleRegion[] = []
    const data = imageData.data
    
    // 创建灰度图像
    const grayData = this.convertToGrayscale(data, canvasWidth, canvasHeight, x, y, width, height)
    
    // 检测水平文本线
    const horizontalLines = this.detectHorizontalTextLines(grayData, width, height)
    
    // 将连续的水平线合并为文本区域
    const textRegions = this.mergeHorizontalLines(horizontalLines, width, height)
    
    // 评估每个区域的字幕可能性
    textRegions.forEach(region => {
      const confidence = this.calculateSubtitleConfidence(
        grayData,
        region,
        width,
        height,
        position
      )
      
      if (confidence > 0.3) { // 置信度阈值
        regions.push({
          x: x + region.x,
          y: y + region.y,
          width: region.width,
          height: region.height,
          confidence: confidence
        })
      }
    })
    
    return regions
  }

  /**
   * 转换为灰度图像
   */
  private convertToGrayscale(
    data: Uint8ClampedArray,
    canvasWidth: number,
    canvasHeight: number,
    x: number,
    y: number,
    width: number,
    height: number
  ): Uint8Array {
    const grayData = new Uint8Array(width * height)
    
    for (let row = 0; row < height; row++) {
      for (let col = 0; col < width; col++) {
        const srcX = x + col
        const srcY = y + row
        
        if (srcX >= canvasWidth || srcY >= canvasHeight) continue
        
        const srcIndex = (srcY * canvasWidth + srcX) * 4
        const r = data[srcIndex]
        const g = data[srcIndex + 1]
        const b = data[srcIndex + 2]
        
        // 灰度转换
        const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b)
        grayData[row * width + col] = gray
      }
    }
    
    return grayData
  }

  /**
   * 检测水平文本线
   */
  private detectHorizontalTextLines(grayData: Uint8Array, width: number, height: number): number[] {
    const horizontalProfile: number[] = new Array(height).fill(0)
    
    // 计算每行的边缘强度
    for (let row = 1; row < height - 1; row++) {
      let edgeStrength = 0
      
      for (let col = 1; col < width - 1; col++) {
        const current = grayData[row * width + col]
        const left = grayData[row * width + col - 1]
        const right = grayData[row * width + col + 1]
        const top = grayData[(row - 1) * width + col]
        const bottom = grayData[(row + 1) * width + col]
        
        // 计算梯度
        const horizontalGradient = Math.abs(right - left)
        const verticalGradient = Math.abs(bottom - top)
        
        // 文本通常有较强的水平边缘
        if (horizontalGradient > this.config.edgeThreshold) {
          edgeStrength += horizontalGradient
        }
      }
      
      horizontalProfile[row] = edgeStrength
    }
    
    return horizontalProfile
  }

  /**
   * 合并水平线为文本区域
   */
  private mergeHorizontalLines(
    horizontalProfile: number[],
    width: number,
    height: number
  ): Array<{ x: number, y: number, width: number, height: number }> {
    const regions: Array<{ x: number, y: number, width: number, height: number }> = []
    
    // 找到强度峰值
    const threshold = Math.max(...horizontalProfile) * 0.3
    let inTextRegion = false
    let regionStart = 0
    
    for (let row = 0; row < height; row++) {
      const strength = horizontalProfile[row]
      
      if (!inTextRegion && strength > threshold) {
        // 开始新的文本区域
        inTextRegion = true
        regionStart = row
      } else if (inTextRegion && strength <= threshold) {
        // 结束当前文本区域
        const regionHeight = row - regionStart
        
        if (regionHeight >= this.config.minTextHeight && regionHeight <= this.config.maxTextHeight) {
          regions.push({
            x: 0,
            y: regionStart,
            width: width,
            height: regionHeight
          })
        }
        
        inTextRegion = false
      }
    }
    
    // 处理最后一个区域
    if (inTextRegion) {
      const regionHeight = height - regionStart
      if (regionHeight >= this.config.minTextHeight && regionHeight <= this.config.maxTextHeight) {
        regions.push({
          x: 0,
          y: regionStart,
          width: width,
          height: regionHeight
        })
      }
    }
    
    return regions
  }

  /**
   * 计算字幕置信度
   */
  private calculateSubtitleConfidence(
    grayData: Uint8Array,
    region: { x: number, y: number, width: number, height: number },
    width: number,
    height: number,
    position: 'top' | 'bottom'
  ): number {
    let confidence = 0
    
    // 1. 水平线特征评分
    const horizontalScore = this.calculateHorizontalLineScore(grayData, region, width)
    confidence += horizontalScore * this.config.horizontalLineWeight
    
    // 2. 位置评分
    const positionScore = position === 'bottom' ? 0.8 : 0.6 // 底部字幕更常见
    confidence += positionScore * this.config.bottomAreaWeight
    
    // 3. 对比度评分
    const contrastScore = this.calculateContrastScore(grayData, region, width)
    confidence += contrastScore * this.config.contrastWeight
    
    return Math.min(confidence, 1.0)
  }

  /**
   * 计算水平线评分
   */
  private calculateHorizontalLineScore(
    grayData: Uint8Array,
    region: { x: number, y: number, width: number, height: number },
    width: number
  ): number {
    let totalEdges = 0
    let horizontalEdges = 0
    
    for (let row = region.y + 1; row < region.y + region.height - 1; row++) {
      for (let col = region.x + 1; col < region.x + region.width - 1; col++) {
        const current = grayData[row * width + col]
        const left = grayData[row * width + col - 1]
        const right = grayData[row * width + col + 1]
        const top = grayData[(row - 1) * width + col]
        const bottom = grayData[(row + 1) * width + col]
        
        const horizontalGradient = Math.abs(right - left)
        const verticalGradient = Math.abs(bottom - top)
        
        if (horizontalGradient > this.config.edgeThreshold || verticalGradient > this.config.edgeThreshold) {
          totalEdges++
          if (horizontalGradient > verticalGradient) {
            horizontalEdges++
          }
        }
      }
    }
    
    return totalEdges > 0 ? horizontalEdges / totalEdges : 0
  }

  /**
   * 计算对比度评分
   */
  private calculateContrastScore(
    grayData: Uint8Array,
    region: { x: number, y: number, width: number, height: number },
    width: number
  ): number {
    let minValue = 255
    let maxValue = 0
    
    for (let row = region.y; row < region.y + region.height; row++) {
      for (let col = region.x; col < region.x + region.width; col++) {
        const value = grayData[row * width + col]
        minValue = Math.min(minValue, value)
        maxValue = Math.max(maxValue, value)
      }
    }
    
    const contrast = maxValue - minValue
    return Math.min(contrast / 255, 1.0)
  }

  /**
   * 获取最佳字幕区域
   */
  getBestSubtitleRegion(canvas: HTMLCanvasElement): SubtitleRegion | null {
    const regions = this.detectSubtitleRegions(canvas)
    return regions.length > 0 ? regions[0] : null
  }

  /**
   * 更新检测配置
   */
  updateConfig(newConfig: Partial<DetectionConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}
