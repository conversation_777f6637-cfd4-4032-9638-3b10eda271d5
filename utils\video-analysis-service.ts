/**
 * 视频分析服务模块
 * 用于视频帧提取、分析和简介生成
 * 支持硬字幕提取功能
 */

import { SubtitleOCRService, SubtitleOCRConfig, OCRResult, DEFAULT_OCR_CONFIG } from './subtitle-ocr-service'
import { SubtitleRegionDetector, DEFAULT_DETECTION_CONFIG } from './subtitle-region-detector'
import { SubtitleTextProcessor, ProcessedSubtitle, DEFAULT_PROCESSING_CONFIG } from './subtitle-text-processor'

export interface VideoAnalysisConfig {
  frameInterval: number // 帧提取间隔（秒）
  skipStartTime: number // 跳过片头时长（秒）
  skipEndTime: number // 跳过片尾时长（秒）
  imageQuality: 'low' | 'medium' | 'high' // 图像质量
  siliconFlowApiKey: string // 硅基流动API密钥
  siliconFlowModel: string // 硅基流动模型

  // 硬字幕提取配置
  enableSubtitleOCR: boolean // 启用硬字幕提取
  ocrConfig: SubtitleOCRConfig // OCR配置
  useOCROnly: boolean // 仅使用OCR，不使用AI分析
}

export interface VideoFrame {
  timestamp: number // 时间戳（秒）
  imageData: ImageData // 图像数据
  base64: string // base64编码的图像
  canvas?: HTMLCanvasElement // canvas元素（用于OCR）
  analysis?: FrameAnalysisResult // AI分析结果
  ocrResult?: OCRResult // OCR分析结果
}

export interface FrameAnalysisResult {
  hasSubtitles: boolean
  subtitleText?: string
  sceneDescription: string
  characterActions: string[]
  keyElements: string[]
  confidence: number
  timestamp: number
}

export interface VideoAnalysisResult {
  frames: VideoFrame[]
  totalDuration: number
  analyzedFrameCount: number
  extractedContent: string // 整合后的内容，类似字幕格式
  episodes: VideoEpisode[] // 分析出的分集内容

  // 硬字幕提取结果
  ocrResults?: OCRResult[] // 原始OCR结果
  processedSubtitles?: ProcessedSubtitle[] // 处理后的字幕
  srtContent?: string // SRT格式字幕
  ocrStatistics?: {
    totalFramesProcessed: number
    successfulExtractions: number
    averageConfidence: number
    totalTextLength: number
  }
}

export interface VideoEpisode {
  episodeNumber: number
  startTime: number
  endTime: number
  content: string // 整合的分析内容
  keyFrames: VideoFrame[] // 关键帧
  wordCount: number
}

export class VideoAnalysisService {
  private config: VideoAnalysisConfig
  private canvas: HTMLCanvasElement | null = null
  private ctx: CanvasRenderingContext2D | null = null

  // 硬字幕提取相关服务
  private ocrService: SubtitleOCRService | null = null
  private regionDetector: SubtitleRegionDetector | null = null
  private textProcessor: SubtitleTextProcessor | null = null

  constructor(config: VideoAnalysisConfig) {
    this.config = config
    this.initCanvas()
    this.initOCRServices()
  }

  private initOCRServices() {
    if (this.config.enableSubtitleOCR) {
      this.ocrService = new SubtitleOCRService(this.config.ocrConfig)
      this.regionDetector = new SubtitleRegionDetector(DEFAULT_DETECTION_CONFIG)
      this.textProcessor = new SubtitleTextProcessor(DEFAULT_PROCESSING_CONFIG)
    }
  }

  private initCanvas() {
    if (typeof window !== 'undefined') {
      this.canvas = document.createElement('canvas')
      this.ctx = this.canvas.getContext('2d')
    }
  }

  /**
   * 分析视频文件
   */
  async analyzeVideo(
    videoFile: File,
    onProgress?: (progress: number, status: string) => void
  ): Promise<VideoAnalysisResult> {
    try {
      onProgress?.(0, '开始视频分析...')

      // 1. 获取视频信息
      const videoInfo = await this.getVideoInfo(videoFile)
      onProgress?.(10, '获取视频信息完成')

      // 2. 提取关键帧
      const frames = await this.extractFrames(videoFile, videoInfo.duration, onProgress)
      onProgress?.(30, `提取了 ${frames.length} 个关键帧`)

      // 3. 初始化OCR服务（如果启用）
      if (this.config.enableSubtitleOCR && this.ocrService) {
        await this.ocrService.initialize((progress, status) => {
          onProgress?.(30 + progress * 0.1, status)
        })
        onProgress?.(40, 'OCR引擎初始化完成')
      }

      // 4. 分析每一帧（AI分析或OCR提取）
      const analyzedFrames = await this.analyzeFrames(frames, onProgress)
      onProgress?.(80, '帧分析完成')

      // 5. 整合分析结果
      const result = await this.integrateAnalysisResults(analyzedFrames, videoInfo.duration)
      onProgress?.(100, '视频分析完成')

      return result
    } catch (error) {
      console.error('视频分析失败:', error)
      throw new Error(`视频分析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取视频基本信息
   */
  private async getVideoInfo(videoFile: File): Promise<{ duration: number, width: number, height: number }> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video')
      video.preload = 'metadata'
      
      video.onloadedmetadata = () => {
        resolve({
          duration: video.duration,
          width: video.videoWidth,
          height: video.videoHeight
        })
        URL.revokeObjectURL(video.src)
      }
      
      video.onerror = () => {
        reject(new Error('无法加载视频文件'))
        URL.revokeObjectURL(video.src)
      }
      
      video.src = URL.createObjectURL(videoFile)
    })
  }

  /**
   * 提取视频帧
   */
  private async extractFrames(
    videoFile: File, 
    duration: number,
    onProgress?: (progress: number, status: string) => void
  ): Promise<VideoFrame[]> {
    const frames: VideoFrame[] = []
    const video = document.createElement('video')
    video.preload = 'metadata'
    video.muted = true

    return new Promise((resolve, reject) => {
      video.onloadedmetadata = async () => {
        try {
          // 计算提取时间点
          const startTime = this.config.skipStartTime
          const endTime = duration - this.config.skipEndTime
          const effectiveDuration = endTime - startTime
          
          if (effectiveDuration <= 0) {
            reject(new Error('有效视频时长太短'))
            return
          }

          const frameCount = Math.floor(effectiveDuration / this.config.frameInterval)
          
          for (let i = 0; i < frameCount; i++) {
            const timestamp = startTime + (i * this.config.frameInterval)
            
            try {
              const frame = await this.extractFrameAtTime(video, timestamp)
              frames.push(frame)
              
              const progress = 10 + (i / frameCount) * 40 // 10-50%的进度
              onProgress?.(progress, `提取第 ${i + 1}/${frameCount} 帧`)
            } catch (error) {
              console.warn(`提取帧失败 (${timestamp}s):`, error)
            }
          }

          URL.revokeObjectURL(video.src)
          resolve(frames)
        } catch (error) {
          URL.revokeObjectURL(video.src)
          reject(error)
        }
      }

      video.onerror = () => {
        URL.revokeObjectURL(video.src)
        reject(new Error('视频加载失败'))
      }

      video.src = URL.createObjectURL(videoFile)
    })
  }

  /**
   * 在指定时间提取帧
   */
  private async extractFrameAtTime(video: HTMLVideoElement, timestamp: number): Promise<VideoFrame> {
    return new Promise((resolve, reject) => {
      const seekHandler = () => {
        try {
          if (!this.canvas || !this.ctx) {
            reject(new Error('Canvas未初始化'))
            return
          }

          // 设置canvas尺寸
          this.canvas.width = video.videoWidth
          this.canvas.height = video.videoHeight

          // 绘制当前帧
          this.ctx.drawImage(video, 0, 0)

          // 获取图像数据
          const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height)
          const base64 = this.canvas.toDataURL('image/jpeg', this.getImageQuality())

          // 为OCR创建canvas副本
          const frameCanvas = document.createElement('canvas')
          frameCanvas.width = this.canvas.width
          frameCanvas.height = this.canvas.height
          const frameCtx = frameCanvas.getContext('2d')!
          frameCtx.drawImage(this.canvas, 0, 0)

          resolve({
            timestamp,
            imageData,
            base64,
            canvas: frameCanvas
          })
        } catch (error) {
          reject(error)
        } finally {
          video.removeEventListener('seeked', seekHandler)
        }
      }

      video.addEventListener('seeked', seekHandler)
      video.currentTime = timestamp
    })
  }

  /**
   * 获取图像质量设置
   */
  private getImageQuality(): number {
    switch (this.config.imageQuality) {
      case 'low': return 0.6
      case 'medium': return 0.8
      case 'high': return 0.95
      default: return 0.8
    }
  }

  /**
   * 分析提取的帧
   */
  private async analyzeFrames(
    frames: VideoFrame[],
    onProgress?: (progress: number, status: string) => void
  ): Promise<VideoFrame[]> {
    const analyzedFrames: VideoFrame[] = []

    for (let i = 0; i < frames.length; i++) {
      const frame = frames[i]

      try {
        // 如果启用OCR且设置为仅使用OCR
        if (this.config.enableSubtitleOCR && this.config.useOCROnly && this.ocrService) {
          // 仅进行OCR处理
          const ocrResult = await this.extractSubtitleFromFrame(frame)
          frame.ocrResult = ocrResult
        } else if (this.config.enableSubtitleOCR && this.ocrService) {
          // 同时进行AI分析和OCR处理
          const [analysis, ocrResult] = await Promise.all([
            this.analyzeFrameWithSiliconFlow(frame),
            this.extractSubtitleFromFrame(frame)
          ])
          frame.analysis = analysis
          frame.ocrResult = ocrResult
        } else {
          // 仅进行AI分析
          const analysis = await this.analyzeFrameWithSiliconFlow(frame)
          frame.analysis = analysis
        }

        analyzedFrames.push(frame)

        const progress = 40 + (i / frames.length) * 40 // 40-80%的进度
        onProgress?.(progress, `分析第 ${i + 1}/${frames.length} 帧`)
        
        // 避免API限流
        if (i < frames.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      } catch (error) {
        console.warn(`帧分析失败 (${frame.timestamp}s):`, error)
        // 即使分析失败也保留帧，但没有分析结果
        analyzedFrames.push(frame)
      }
    }

    return analyzedFrames
  }

  /**
   * 从帧中提取字幕（OCR）
   */
  private async extractSubtitleFromFrame(frame: VideoFrame): Promise<OCRResult | null> {
    if (!this.ocrService || !frame.canvas) {
      return null
    }

    try {
      return await this.ocrService.extractSubtitleFromFrame(
        frame.canvas,
        frame.timestamp
      )
    } catch (error) {
      console.warn(`OCR提取失败 (${frame.timestamp}s):`, error)
      return null
    }
  }

  /**
   * 使用硅基流动API分析单帧
   */
  private async analyzeFrameWithSiliconFlow(frame: VideoFrame): Promise<FrameAnalysisResult> {
    const response = await fetch('/api/video-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        image: frame.base64,
        timestamp: frame.timestamp,
        apiKey: this.config.siliconFlowApiKey,
        model: this.config.siliconFlowModel
      })
    })

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status}`)
    }

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'API调用失败')
    }

    return data.analysis
  }

  /**
   * 整合分析结果
   */
  private async integrateAnalysisResults(
    frames: VideoFrame[],
    totalDuration: number
  ): Promise<VideoAnalysisResult> {
    // 按时间排序
    frames.sort((a, b) => a.timestamp - b.timestamp)

    // 处理OCR结果
    let ocrResults: OCRResult[] = []
    let processedSubtitles: ProcessedSubtitle[] = []
    let srtContent = ''
    let ocrStatistics = {
      totalFramesProcessed: 0,
      successfulExtractions: 0,
      averageConfidence: 0,
      totalTextLength: 0
    }

    if (this.config.enableSubtitleOCR && this.textProcessor) {
      // 收集所有OCR结果
      ocrResults = frames
        .map(frame => frame.ocrResult)
        .filter((result): result is OCRResult => result !== null && result !== undefined)

      if (ocrResults.length > 0) {
        // 处理OCR结果
        processedSubtitles = this.textProcessor.processOCRResults(ocrResults)
        srtContent = this.textProcessor.generateSRT(processedSubtitles)

        // 计算统计信息
        const stats = this.textProcessor.getStatistics(processedSubtitles)
        ocrStatistics = {
          totalFramesProcessed: frames.length,
          successfulExtractions: ocrResults.length,
          averageConfidence: stats.averageConfidence,
          totalTextLength: stats.totalDuration
        }
      }
    }

    // 提取所有分析内容，生成类似字幕的格式
    const contentParts: string[] = []
    const subtitleLikeContent: string[] = []

    frames.forEach((frame, index) => {
      // 优先使用OCR结果，其次使用AI分析结果
      let contentDescription = ''
      let hasContent = false

      // 1. 尝试使用OCR结果
      if (frame.ocrResult && frame.ocrResult.text.trim()) {
        contentDescription = frame.ocrResult.text.trim()
        hasContent = true
      }
      // 2. 如果没有OCR结果，使用AI分析结果
      else if (frame.analysis) {
        if (frame.analysis.subtitleText && frame.analysis.subtitleText.trim()) {
          contentDescription = frame.analysis.subtitleText.trim()
          hasContent = true
        } else {
          // 使用场景描述和行为分析
          const parts: string[] = []

          if (frame.analysis.sceneDescription) {
            parts.push(frame.analysis.sceneDescription)
          }

          if (frame.analysis.characterActions.length > 0) {
            parts.push(`人物行为: ${frame.analysis.characterActions.join(', ')}`)
          }

          if (frame.analysis.keyElements.length > 0) {
            parts.push(`关键要素: ${frame.analysis.keyElements.join(', ')}`)
          }

          contentDescription = parts.join('; ') || '画面内容分析'
          hasContent = true
        }
      }

      if (hasContent) {
        const timeStr = this.formatTimestamp(frame.timestamp)

        // 生成类似字幕的时间戳格式
        const nextTimestamp = index < frames.length - 1 ? frames[index + 1].timestamp : frame.timestamp + this.config.frameInterval
        const srtTimeFormat = `${this.toSRTTimeFormat(frame.timestamp)} --> ${this.toSRTTimeFormat(nextTimestamp)}`

        // 生成SRT格式的条目
        subtitleLikeContent.push(`${index + 1}`)
        subtitleLikeContent.push(srtTimeFormat)
        subtitleLikeContent.push(contentDescription)
        subtitleLikeContent.push('') // 空行分隔

        // 生成简化的内容摘要
        const frameSummary = `[${timeStr}] ${contentDescription}`
        contentParts.push(frameSummary)
      }
    })

    // 生成整合内容（两种格式）
    const extractedContent = subtitleLikeContent.join('\n') // SRT格式，用于字幕解析
    const summaryContent = contentParts.join('\n') // 摘要格式，用于显示

    // 智能分集划分
    const episodes = this.divideIntoEpisodesIntelligent(frames, extractedContent, totalDuration)

    return {
      frames,
      totalDuration,
      analyzedFrameCount: frames.filter(f => f.analysis || f.ocrResult).length,
      extractedContent,
      episodes,

      // OCR相关结果
      ocrResults: ocrResults.length > 0 ? ocrResults : undefined,
      processedSubtitles: processedSubtitles.length > 0 ? processedSubtitles : undefined,
      srtContent: srtContent || undefined,
      ocrStatistics: ocrResults.length > 0 ? ocrStatistics : undefined
    }
  }

  /**
   * 转换为SRT时间格式
   */
  private toSRTTimeFormat(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    const milliseconds = Math.floor((seconds % 1) * 1000)

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`
  }

  /**
   * 智能分集划分
   */
  private divideIntoEpisodesIntelligent(frames: VideoFrame[], content: string, totalDuration: number): VideoEpisode[] {
    // 如果视频时长较短（小于30分钟），作为一集处理
    if (totalDuration < 1800) {
      return [{
        episodeNumber: 1,
        startTime: 0,
        endTime: frames.length > 0 ? frames[frames.length - 1].timestamp : totalDuration,
        content: content,
        keyFrames: frames.filter(f => f.analysis && f.analysis.confidence > 0.7),
        wordCount: content.length
      }]
    }

    // 对于较长的视频，尝试智能分集
    const episodes: VideoEpisode[] = []
    const episodeDuration = 1800 // 假设每集30分钟
    const episodeCount = Math.ceil(totalDuration / episodeDuration)

    for (let i = 0; i < episodeCount; i++) {
      const startTime = i * episodeDuration
      const endTime = Math.min((i + 1) * episodeDuration, totalDuration)

      // 获取该时间段内的帧
      const episodeFrames = frames.filter(f => f.timestamp >= startTime && f.timestamp < endTime)

      // 提取该时间段的内容
      const episodeContentParts: string[] = []
      episodeFrames.forEach(frame => {
        if (frame.analysis) {
          let description = ''
          if (frame.analysis.subtitleText) {
            description = frame.analysis.subtitleText
          } else {
            const parts = [
              frame.analysis.sceneDescription,
              frame.analysis.characterActions.join(', '),
              frame.analysis.keyElements.join(', ')
            ].filter(p => p && p.trim()).join('; ')
            description = parts || '画面内容'
          }
          episodeContentParts.push(description)
        }
      })

      const episodeContent = episodeContentParts.join(' ')

      episodes.push({
        episodeNumber: i + 1,
        startTime,
        endTime,
        content: episodeContent,
        keyFrames: episodeFrames.filter(f => f.analysis && f.analysis.confidence > 0.7),
        wordCount: episodeContent.length
      })
    }

    return episodes
  }

  /**
   * 格式化时间戳
   */
  private formatTimestamp(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    } else {
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
  }

  /**
   * 估算API调用成本
   */
  estimateCost(videoDuration: number): { frameCount: number, estimatedCost: number } {
    const effectiveDuration = videoDuration - this.config.skipStartTime - this.config.skipEndTime
    const frameCount = Math.max(1, Math.floor(effectiveDuration / this.config.frameInterval))

    // 假设每次API调用成本约0.01元（需要根据实际情况调整）
    const estimatedCost = frameCount * 0.01

    return { frameCount, estimatedCost }
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 清理canvas资源
    if (this.canvas) {
      this.canvas.remove()
      this.canvas = null
      this.ctx = null
    }
  }

  /**
   * 验证配置参数
   */
  static validateConfig(config: VideoAnalysisConfig): { valid: boolean, errors: string[] } {
    const errors: string[] = []

    if (config.frameInterval < 1 || config.frameInterval > 300) {
      errors.push('帧提取间隔必须在1-300秒之间')
    }

    if (config.skipStartTime < 0 || config.skipStartTime > 3600) {
      errors.push('跳过片头时长必须在0-3600秒之间')
    }

    if (config.skipEndTime < 0 || config.skipEndTime > 3600) {
      errors.push('跳过片尾时长必须在0-3600秒之间')
    }

    if (!config.siliconFlowApiKey || config.siliconFlowApiKey.trim().length === 0) {
      errors.push('硅基流动API密钥不能为空')
    }

    if (!['low', 'medium', 'high'].includes(config.imageQuality)) {
      errors.push('图像质量设置无效')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    if (this.ocrService) {
      await this.ocrService.cleanup()
      this.ocrService = null
    }

    this.regionDetector = null
    this.textProcessor = null
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<VideoAnalysisConfig>): void {
    this.config = { ...this.config, ...newConfig }

    // 如果OCR配置发生变化，重新初始化OCR服务
    if (newConfig.enableSubtitleOCR !== undefined || newConfig.ocrConfig) {
      this.initOCRServices()
    }
  }

  /**
   * 获取OCR统计信息
   */
  getOCRStatistics(): any {
    if (!this.config.enableSubtitleOCR) {
      return null
    }

    return {
      enabled: this.config.enableSubtitleOCR,
      ocrEngine: this.config.ocrConfig?.ocrEngine || 'tesseract',
      language: this.config.ocrConfig?.language || 'chi_sim+eng',
      useOCROnly: this.config.useOCROnly
    }
  }
}
